import { useTranslation } from "next-i18next";
import { Comment } from "@/components/Comment";
import {
  Box,
  Button,
  Card,
  Center,
  Divider,
  Group,
  Loader,
  Pagination,
  Paper,
  Rating,
  Stack,
  Text,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import { useCallback, useMemo, useState } from "react";
import RatingModal from "../RatingModal";
import { useQuery } from "react-query";
import { ApiService } from "../../../api";
import { IReview } from "@/interfaces/IReview";
import { formatDistanceToNow } from "date-fns";
import ReviewAnalysis from "@/components/ReviewAnalysis";
import { useRouter } from "next/router";
import { IconMoodEmpty } from "@tabler/icons-react";
import { IReviewAnalysis } from "@/interfaces/IReviewAnalysis";
import { notifications } from "@mantine/notifications";
import { useProfile } from "@/store";
import { PopularityChart } from "@/components/PopularityChart";
import { useLeaderHover } from "@/contexts/LeaderHoverContext";
import Poll from "@/components/Poll";

const DEFAULT_PAGE_SIZE = 10;

const PollsContainer = (props: {
  entityType: string;
  entityId: string;
  onlyAnalysis?: boolean;
}) => {
  const { t } = useTranslation();
  const [opened, { open, close }] = useDisclosure(false);
  const [rate, setRate] = useState(0);
  const router = useRouter();
  const profile = useProfile();
  const { openLoginModal } = useLeaderHover();
  const page = router.query.page as string;
  const pollsQuery = useQuery<{ totalItems: number; items: IReview[] }>(
    ["PollsContainer", page, props.entityType, props.entityId],
    async () => {
      const response = await ApiService.getReviews(
        props.entityType,
        props.entityId as string,
        {
          page: (page || 1) as string,
          limit: DEFAULT_PAGE_SIZE,
        }
      );
      return response.data?.data;
    }
  );
  const reviewAnalysisQuery = useQuery<IReviewAnalysis>(
    ["PollsContaineranalysis", props.entityType, props.entityId],
    async () => {
      const response = await ApiService.getReviewAnalysis(
        props.entityType,
        props.entityId
      );
      return response.data?.data;
    }
  );
  const total = useMemo(() => {
    //@ts-expect-error
    return Math.ceil(pollsQuery.data?.totalItems / DEFAULT_PAGE_SIZE);
  }, [pollsQuery.data?.items]);

  const handlePagination = useCallback((page: number) => {
    const nexturl = new URLSearchParams(
      router.asPath + "?" + window.location.search
    );
    nexturl.set("page", page + "");
    router.push({
      pathname: router.pathname,
      query: {
        ...router.query,
        page,
      },
    });
  }, []);

  return (
    <>
      <RatingModal
        rateOnId={props.entityId}
        rateOnType={props.entityType}
        onClose={() => {
          close();
          pollsQuery.refetch();
          setRate(0);
        }}
        opened={opened}
        rate={rate}
      />
      <Stack gap={5} align="center" w={"100%"}>
        <Card w={"100%"} shadow="0">
          <Group w="100%" justify="space-between" mt={10} wrap="nowrap">
            <Rating
              value={rate}
              size="xl"
              readOnly={!profile.profile?.firstName}
              onChange={(e) =>
                profile.profile?.firstName && (setRate(e), open())
              }
            />
            <Button
              color="cyan"
              onClick={() => {
                if (!profile.profile?.firstName) {
                  openLoginModal();
                  notifications.show({
                    message: t("common:please_login_to_rate"),
                    color: "red",
                  });
                  return;
                }
                open();
              }}
            >
              {t("common:write-review")}
            </Button>
          </Group>
        </Card>
        <ReviewAnalysis
          average={reviewAnalysisQuery.data?.average || 0}
          rates={reviewAnalysisQuery.data?.rates || {}}
          count={reviewAnalysisQuery.data?.count || 0}
        />{" "}
        <Box w={"100%"}>
          <PopularityChart
            resourceId={props.entityId}
            resourceType={props.entityType}
          />
        </Box>
        <Box p={"xs"} w={"100%"}>
          {pollsQuery.data?.items.length ? (
            pollsQuery.data?.items.map((review, index) => (
              <Box key={index}>
                <Poll
                  id={review.id + ""}
                  title={""}
                  description={""}
                  options={""}
                  mode={review.mode}
                  expiresAt={review.expiresAt}
                  totalVotes={review.totalVotes}
                  hasVoted={review.hasVoted}
                  userVotes={review.userVotes}
                />
              </Box>
            ))
          ) : (
            <Center w={"100%"} mih={"15rem"}>
              {pollsQuery.isLoading || pollsQuery.isFetching ? (
                <Loader />
              ) : (
                <Stack align="center" gap={-20}>
                  <IconMoodEmpty size={100} color="#c2c2c2" />
                  <Text size={"xl"} c={"#c2c2c2"}>
                    {t("common:no-reviews-yet")}
                  </Text>
                </Stack>
              )}
            </Center>
          )}
        </Box>
        {total ? (
          <Pagination
            onChange={handlePagination}
            total={total}
            value={+page || 1}
            color="gray"
            size="sm"
            radius="md"
          />
        ) : null}
      </Stack>
    </>
  );
};
export default PollsContainer;
