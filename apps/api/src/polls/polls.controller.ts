import {
  Controller,
  Get,
  Post,
  Param,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { SystemPollsService } from './system-polls.service';
import { RESOURCE_TYPE } from '@prisma/client';
import { PollsService } from './polls.service';

@Controller('polls')
export class PollsController {
  constructor(private readonly systemPollsService: SystemPollsService) {}

  @Get(':resourceType/:resourceId')
  async getSystemPolls(
    @Param('resourceType') resourceType: string,
    @Param('resourceId') resourceId: string,
    @Req() req,
    @Query('includeUserStatus') includeUserStatus?: string,
  ) {
    const userId = req.user?.id;
    const resType = resourceType.toUpperCase() as RESOURCE_TYPE;
    const resId = parseInt(resourceId);

    if (includeUserStatus === 'true' && userId) {
      return this.systemPollsService.getSystemPollsWithUserStatus(
        resId,
        resType,
        userId,
      );
    }

    return this.systemPollsService.getOrCreateSystemPolls(resId, resType);
  }

  @Get(':resourceType/:resourceId/results')
  async getSystemPollsResults(
    @Param('resourceType') resourceType: string,
    @Param('resourceId') resourceId: string,
  ) {
    const resType = resourceType.toUpperCase() as RESOURCE_TYPE;
    const resId = parseInt(resourceId);

    return this.systemPollsService.getSystemPollsResults(resId, resType);
  }

  @Post(':resourceType/:resourceId/regenerate')
  async regenerateSystemPolls(
    @Param('resourceType') resourceType: string,
    @Param('resourceId') resourceId: string,
  ) {
    const resType = resourceType.toUpperCase() as RESOURCE_TYPE;
    const resId = parseInt(resourceId);

    return this.systemPollsService.regenerateSystemPolls(resId, resType);
  }

  @Get(':resourceType/:resourceId/exists')
  async checkSystemPollsExist(
    @Param('resourceType') resourceType: string,
    @Param('resourceId') resourceId: string,
  ) {
    const resType = resourceType.toUpperCase() as RESOURCE_TYPE;
    const resId = parseInt(resourceId);

    const exists = await this.systemPollsService.hasSystemPolls(resId, resType);
    return { exists };
  }

  @Get('templates/:resourceType')
  getAvailableTemplates(@Param('resourceType') resourceType: string) {
    const resType = resourceType.toUpperCase() as RESOURCE_TYPE;
    return this.systemPollsService.getAvailableTemplates(resType);
  }
}
