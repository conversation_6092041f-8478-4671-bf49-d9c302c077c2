import {
  <PERSON>,
  Get,
  Post,
  Param,
  Query,
  ParseIntPipe,
  ParseEnumPipe,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { RESOURCE_TYPE } from '@prisma/client';
import { SystemPollsService } from './system-polls.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from 'src/modules/auth/jwt-auth.guard';

@ApiTags('System Polls')
@Controller('system-polls')
export class SystemPollsController {
  private readonly logger = new Logger(SystemPollsController.name);

  constructor(private readonly systemPollsService: SystemPollsService) {}

  @Get(':resourceType/:resourceId')
  @ApiOperation({ summary: 'Get or create system polls for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiQuery({
    name: 'validate',
    type: 'boolean',
    required: false,
    description: 'Validate if resource exists',
  })
  @ApiResponse({
    status: 200,
    description: 'System polls retrieved/created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid resource type or ID' })
  @ApiResponse({ status: 404, description: 'Resource not found' })
  async getOrCreateSystemPolls(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
    @Query('validate') validate: boolean = true,
  ) {
    this.logger.log(`Getting system polls for ${resourceType}:${resourceId}`);

    return this.systemPollsService.getOrCreateSystemPolls(
      resourceId,
      resourceType,
      validate,
    );
  }

  @Post(':resourceType/:resourceId/create')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Force create system polls for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 201,
    description: 'System polls created successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid resource type or ID' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 409, description: 'Polls already exist' })
  async forceCreateSystemPolls(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    this.logger.log(
      `Force creating system polls for ${resourceType}:${resourceId}`,
    );

    return this.systemPollsService.forceCreateSystemPolls(
      resourceId,
      resourceType,
    );
  }

  @Get(':resourceType/:resourceId/exists')
  @ApiOperation({ summary: 'Check if system polls exist for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Poll existence status returned' })
  async checkSystemPollsExist(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    const exists = await this.systemPollsService.systemPollsExist(
      resourceId,
      resourceType,
    );
    return { exists };
  }

  @Get(':resourceType/:resourceId/statistics')
  @ApiOperation({ summary: 'Get poll statistics for a resource' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({
    status: 200,
    description: 'Poll statistics retrieved successfully',
  })
  async getPollStatistics(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    return this.systemPollsService.getPollStatistics(resourceId, resourceType);
  }

  @Get('trending')
  @ApiOperation({ summary: 'Get trending system polls' })
  @ApiQuery({ name: 'resourceType', enum: RESOURCE_TYPE, required: false })
  @ApiQuery({
    name: 'limit',
    type: 'number',
    required: false,
    description: 'Number of polls to return (default: 10)',
  })
  @ApiResponse({
    status: 200,
    description: 'Trending polls retrieved successfully',
  })
  async getTrendingPolls(
    @Query('resourceType') resourceType?: RESOURCE_TYPE,
    @Query('limit', new ParseIntPipe()) limit: number = 10,
  ) {
    return this.systemPollsService.getTrendingPolls(resourceType, limit);
  }

  @Post('poll/:pollId/update-counts')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Update vote counts for a poll' })
  @ApiParam({ name: 'pollId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Vote counts updated successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updatePollVoteCounts(@Param('pollId', ParseIntPipe) pollId: number) {
    await this.systemPollsService.updatePollVoteCounts(pollId);
    return { message: 'Vote counts updated successfully' };
  }

  @Get('resource/:resourceType/:resourceId/validate')
  @ApiOperation({ summary: 'Validate if a resource exists' })
  @ApiParam({ name: 'resourceType', enum: RESOURCE_TYPE })
  @ApiParam({ name: 'resourceId', type: 'number' })
  @ApiResponse({ status: 200, description: 'Resource validation result' })
  @ApiResponse({ status: 400, description: 'Resource does not exist' })
  async validateResource(
    @Param('resourceType', new ParseEnumPipe(RESOURCE_TYPE))
    resourceType: RESOURCE_TYPE,
    @Param('resourceId', ParseIntPipe) resourceId: number,
  ) {
    const exists = await this.systemPollsService.validateResource(
      resourceId,
      resourceType,
    );
    return { exists, resourceType, resourceId };
  }
}
