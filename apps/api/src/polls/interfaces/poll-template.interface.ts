import { RESOURCE_TYPE, POLL_TYPE } from '@prisma/client';

export interface PollTemplate {
  question: string;
  questionLocal?: string;
  title: string;
  titleLocal?: string;
  description?: string;
  descriptionLocal?: string;
  type: POLL_TYPE;
  options: {
    text: string;
    textLocal?: string;
    value: string;
  }[];
}

export interface ResourcePollTemplate {
  resourceType: RESOURCE_TYPE;
  templates: PollTemplate[];
}
