{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"studio": "prisma studio", "prebuild": "<PERSON><PERSON><PERSON> dist", "dev": "yarn run start:dev", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prisma": "prisma generate", "prisma:migrate-prod": "prisma migrate deploy"}, "prisma": {"seed": "ts-node  --transpile-only prisma/seed.ts"}, "dependencies": {"@nestjs/swagger": "11.2.0", "@adminjs/design-system": "4.1.1", "react-insta-stories": "2.8.0", "@types/express-session": "1.18.1", "passport-twitter": "1.0.4", "brave-search": "0.9.0", "@nestjs/axios": "4.0.0", "ollama": "0.5.15", "casl": "1.1.0", "@casl/ability": "6.7.3", "express-basic-auth": "^1.2.1", "@bull-board/api": "5.21.4", "@bull-board/express": "5.21.4", "bull-board": "2.1.3", "bullmq": "5.12.14", "@nestjs/bullmq": "10.2.1", "bikram-sambat-js": "1.0.2", "html-metadata-parser": "2.0.4", "@mozilla/readability": "0.6.0", "jsdom": "26.1.0", "@adminjs/express": "6.1.1", "@adminjs/nestjs": "6.1.0", "@adminjs/prisma": "5.0.3", "@apollo/server": "4.10.1", "@indic-transliteration/sanscript": "1.2.8", "@keyv/redis": "4.3.4", "@nestjs-modules/mailer": "1.8.1", "@nestjs/apollo": "12.1.0", "@nestjs/cache-manager": "2.2.2", "@nestjs/common": "9.0.0", "@nestjs/config": "2.3.1", "@nestjs/core": "9.0.0", "@nestjs/event-emitter": "1.4.1", "@nestjs/graphql": "12.1.1", "@nestjs/jwt": "10.0.3", "@nestjs/mapped-types": "*", "@nestjs/microservices": "9.3.12", "@nestjs/passport": "9.0.3", "@nestjs/platform-express": "9.0.0", "@nestjs/schedule": "6.0.0", "@nestjs/throttler": "^4.0.0", "@prisma/client": "6.7.0", "@sindresorhus/transliterate": "1.6.0", "@types/bcrypt": "5.0.0", "@types/email-templates": "10.0.4", "@types/lodash": "4.14.191", "@types/multer": "1.4.11", "@types/passport-facebook": "3.0.3", "@types/passport-google-oauth20": "2.0.16", "@types/passport-jwt": "3.0.8", "@types/passport-local": "1.0.35", "adminjs": "^7.8.15", "axios": "1.4.0", "bcrypt": "5.1.1", "cache-manager": "5.1.4", "cache-manager-redis-store": "2.0.0", "cacheable": "1.8.10", "class-transformer": "0.5.1", "class-validator": "0.14.0", "date-fns": "2.30.0", "ejs": "3.1.10", "email-templates": "12.0.1", "express-formidable": "^1.2.0", "express-session": "^1.18.1", "google-spreadsheet": "3.3.0", "graphql": "16.8.1", "graphql-type-json": "0.3.2", "helmet": "7.0.0", "hpp": "0.2.3", "jsonwebtoken": "9.0.1", "keyv": "5.3.3", "kysely": "0.27.2", "lodash": "4.17.21", "nest-cloudflare-turnstile": "0.0.12", "nestjs-prisma": "0.25.0", "node-xlsx": "0.21.2", "openai": "4.93.0", "passport": "0.6.0", "passport-facebook": "3.0.0", "passport-google-oauth20": "2.0.0", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "prisma": "6.7.0", "prisma-extension-kysely": "2.1.0", "prisma-graphql-type-decimal": "3.0.0", "prisma-kysely": "1.8.0", "prisma-nestjs-graphql": "20.0.2", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.2.0", "toobusy-js": "0.5.1", "transliteration": "2.3.5", "ts-morph": "*", "twitter-api-v2": "1.22.0", "type-graphql": "1.1.1", "typegraphql-prisma": "0.27.2", "wikipedia": "2.1.0"}, "devDependencies": {"@nestjs/cli": "9.0.0", "@nestjs/schematics": "9.0.0", "@nestjs/testing": "9.0.0", "@types/express": "4.17.13", "@types/jest": "28.1.8", "@types/node": "16.0.0", "@types/nodemailer": "6.4.7", "@types/supertest": "2.0.11", "@types/uuid": "9.0.1", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "8.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "5.0.0", "jest": "28.1.3", "nodemailer": "6.10.0", "prettier": "2.3.2", "source-map-support": "0.5.20", "supertest": "6.1.3", "ts-jest": "28.0.8", "ts-loader": "9.2.3", "ts-node": "10.0.0", "tsconfig-paths": "4.1.0", "typescript": "4.7.4"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {".+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "resolutions": {"wrap-ansi": "7.0.0", "string-width": "4.1.0"}}